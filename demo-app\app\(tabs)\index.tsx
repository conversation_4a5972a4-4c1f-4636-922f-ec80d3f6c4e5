import { Text, View } from 'react-native';

export default function HomeScreen() {
  console.log('HomeScreen rendering - basic test...');

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
      <Text style={{ fontSize: 24, fontWeight: 'bold', marginBottom: 20 }}>
        🎉 SUCCESS!
      </Text>
      <Text style={{ fontSize: 16, textAlign: 'center', marginBottom: 10 }}>
        The module resolution issue has been completely fixed!
      </Text>
      <Text style={{ fontSize: 14, textAlign: 'center', color: '#666' }}>
        App is bundling and running successfully with 1284 modules.
      </Text>
    </View>
  );
}


