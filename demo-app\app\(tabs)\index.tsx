import React, { useState } from 'react';
import { SafeAreaView, ScrollView, View, Text, StyleSheet, Switch } from 'react-native';
// Temporarily comment out primenative import to test basic functionality
// import { ThemeProvider, useTheme, Button, OTPInput } from 'primenative';

export default function HomeScreen() {
  console.log('HomeScreen rendering...');

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>PrimeNative Demo - Basic Test</Text>
        <Text style={styles.subtitle}>If you see this, Expo Router is working</Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Section</Text>
          <View style={styles.sectionContent}>
            <Text>This is a basic test without primenative components</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: 'center',
    color: '#666',
  },
  section: {
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    padding: 12,
    backgroundColor: '#f5f5f5',
  },
  sectionContent: {
    padding: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  themeToggle: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  label: {
    marginBottom: 8,
    fontSize: 14,
  },
  valueText: {
    marginTop: 8,
    fontSize: 12,
    color: '#666',
  },
});
