{"name": "demo-app", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@types/react": "~19.0.10", "expo": "^53.0.12", "expo-haptics": "^14.1.4", "expo-image": "^2.3.0", "expo-linking": "~7.1.5", "expo-router": "^5.1.0", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "react": "19.0.0", "react-native": "0.79.4", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "typescript": "^5.4.5"}, "devDependencies": {"@babel/core": "^7.24.0"}, "private": true}